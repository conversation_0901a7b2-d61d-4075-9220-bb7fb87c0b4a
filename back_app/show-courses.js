const mongoose = require('mongoose');
const Course = require('./src/models/Course');
require('dotenv').config();

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb+srv://mstorsulam786:<EMAIL>/zero_koin');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Display courses with details
const showCourses = async () => {
  try {
    const courses = await Course.find().sort({ createdAt: -1 });

    if (courses.length === 0) {
      console.log('📚 No courses found in the database');
      return;
    }

    console.log(`\n📚 Found ${courses.length} course(s):\n`);
    console.log('='.repeat(80));

    courses.forEach((course, index) => {
      console.log(`\n${index + 1}. Course Details:`);
      console.log(`   📖 Name: ${course.courseName}`);
      console.log(`   🆔 ID: ${course._id}`);
      console.log(`   📄 Pages: ${course.pages.length} page(s)`);

      // Extract time from pages if available
      const pageTime = course.pages.length > 0 && course.pages[0].time ? course.pages[0].time : null;
      console.log(`   ⏰ Time: ${pageTime || 'Not specified'}`);

      console.log(`   ✅ Active: ${course.isActive ? 'Yes' : 'No'}`);
      console.log(`   👤 Uploaded by: ${course.uploadedBy || 'Unknown'}`);
      console.log(`   📅 Created: ${course.createdAt.toLocaleDateString()}`);
      console.log(`   🔄 Updated: ${course.updatedAt.toLocaleDateString()}`);

      // Show page details
      if (course.pages.length > 0) {
        console.log(`   📑 Page Details:`);
        course.pages.forEach((page, pageIndex) => {
          console.log(`      ${pageIndex + 1}. Title: ${page.title || 'No title'}`);
          console.log(`         Time: ${page.time || 'No time'}`);
          console.log(`         Content: ${page.content ? page.content.substring(0, 50) + '...' : 'No content'}`);
        });
      }

      console.log('-'.repeat(50));
    });

  } catch (error) {
    console.error('❌ Error fetching courses:', error);
  }
};

// Main execution
const main = async () => {
  await connectDB();
  await showCourses();
  mongoose.connection.close();
  console.log('\n✅ Database connection closed');
};

main();
