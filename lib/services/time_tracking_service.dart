import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:zero_koin/controllers/session_controller.dart';

class TimeTrackingService extends GetxService {
  static const String baseUrl =
      'https://zero-koin-backend.vercel.app/api/users';

  Timer? _timeTrackingTimer;
  bool _isTracking = false;

  // Observable for time manipulation detection
  final RxBool timeManipulationDetected = false.obs;
  final RxInt timeChangeCount = 0.obs;
  final RxBool isTimeManipulator = false.obs;

  @override
  void onInit() {
    super.onInit();
    print('🕐 TimeTrackingService onInit called');
    startTimeTracking();
  }

  @override
  void onClose() {
    stopTimeTracking();
    super.onClose();
  }

  void startTimeTracking() {
    if (_isTracking) return;

    _isTracking = true;
    print('🕐 Starting time tracking service...');

    // Record initial time
    recordCurrentTime();

    // Start periodic time checking every 30 seconds
    _timeTrackingTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      checkTimeManipulation();
    });
  }

  void stopTimeTracking() {
    _timeTrackingTimer?.cancel();
    _isTracking = false;
    print('🕐 Time tracking service stopped');
  }

  Future<void> recordCurrentTime() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final token = await user.getIdToken();
      final currentTime = DateTime.now().toIso8601String();

      final response = await http.post(
        Uri.parse('$baseUrl/record-time'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({'mobileTime': currentTime}),
      );

      if (response.statusCode == 200) {
        print('✅ Time recorded successfully');
      } else {
        print('❌ Failed to record time: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error recording time: $e');
    }
  }

  Future<void> checkTimeManipulation() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final token = await user.getIdToken();
      final currentTime = DateTime.now().toIso8601String();

      final response = await http.post(
        Uri.parse('$baseUrl/check-time-manipulation'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({'currentMobileTime': currentTime}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        final bool manipulationDetected =
            data['timeManipulationDetected'] ?? false;
        final int changeCount = data['changeCount'] ?? 0;
        final bool isManipulator = data['isTimeManipulator'] ?? false;

        // Update observables
        timeManipulationDetected.value = manipulationDetected;
        timeChangeCount.value = changeCount;
        isTimeManipulator.value = isManipulator;

        if (manipulationDetected) {
          print('⚠️ Time manipulation detected!');
          print('📊 Change count: $changeCount');
          print('🚨 Is time manipulator: $isManipulator');

          _showTimeManipulationDialog(data);
        }
      } else {
        print('❌ Failed to check time manipulation: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error checking time manipulation: $e');
    }
  }

  void _showTimeManipulationDialog(Map<String, dynamic> data) {
    if (Get.isDialogOpen == true) return;

    // Show snackbar first
    Get.snackbar(
      'Time Manipulation Detected',
      'Device time has been changed manually. This may affect countdown timers.',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 5),
      isDismissible: true,
      margin: const EdgeInsets.all(16),
    );

    // Show dialog
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!Get.isDialogOpen!) {
        Get.dialog(
          AlertDialog(
            title: const Text(
              'Time Manipulation Detected',
              style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Device time has been changed manually.',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 8),
                Text(
                  'Time difference: ${data['timeDifference']?.toStringAsFixed(1) ?? 'Unknown'} seconds',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
                Text(
                  'Detection count: ${data['changeCount'] ?? 0}',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
                if (data['isTimeManipulator'] == true)
                  const Text(
                    'Warning: Multiple time changes detected',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
          barrierDismissible: false,
        );

        // Auto-close dialog after 4 seconds
        Future.delayed(const Duration(seconds: 4), () {
          if (Get.isDialogOpen!) {
            Get.back();
            // Refresh session data
            try {
              final sessionController = Get.find<SessionController>();
              sessionController.loadSessions();
            } catch (e) {
              print('SessionController not available: $e');
            }
          }
        });
      }
    });
  }

  // Manual trigger for testing
  void triggerTimeCheck() {
    checkTimeManipulation();
  }

  // Get current status
  Map<String, dynamic> getStatus() {
    return {
      'isTracking': _isTracking,
      'timeManipulationDetected': timeManipulationDetected.value,
      'timeChangeCount': timeChangeCount.value,
      'isTimeManipulator': isTimeManipulator.value,
    };
  }
}
