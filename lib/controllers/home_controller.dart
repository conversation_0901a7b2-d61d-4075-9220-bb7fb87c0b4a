import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zero_koin/controllers/user_controller.dart';
import 'package:zero_koin/controllers/session_controller.dart';

class HomeController extends GetxController {
  final UserController userController = Get.find<UserController>();

  // Time change detection variables
  Timer? _timeCheckTimer;
  DateTime? _lastKnownTime;
  static const Duration _timeCheckInterval = Duration(seconds: 2);
  static const Duration _timeChangeThreshold = Duration(seconds: 3);

  // Add any other observable variables or initial data loading specific to HomeScreen here
  // For now, we primarily rely on UserController's observables.

  @override
  void onInit() {
    super.onInit();
    print('HomeController onInit called');
    // Initial data loading for HomeScreen, if any
    _initializeTimeChangeDetection();
  }

  @override
  void onClose() {
    _timeCheckTimer?.cancel();
    super.onClose();
  }

  void _initializeTimeChangeDetection() {
    print('Initializing time change detection...');
    _lastKnownTime = DateTime.now();
    print('Last known time set to: $_lastKnownTime');
    _startTimeChangeDetection();
  }

  void _startTimeChangeDetection() {
    print('Starting time change detection timer...');
    _timeCheckTimer = Timer.periodic(_timeCheckInterval, (timer) {
      _checkForTimeChange();
    });
    print(
      'Time change detection timer started with interval: $_timeCheckInterval',
    );
  }

  void _checkForTimeChange() {
    if (_lastKnownTime == null) return;

    final currentTime = DateTime.now();
    final expectedTime = _lastKnownTime!.add(_timeCheckInterval);
    final timeDifference = currentTime.difference(expectedTime).abs();

    // Debug logging
    print(
      'Time check - Current: $currentTime, Expected: $expectedTime, Difference: ${timeDifference.inSeconds}s',
    );

    // If the time difference is greater than threshold, time was likely changed manually
    if (timeDifference > _timeChangeThreshold) {
      print('Time change detected! Showing dialog...');
      _showTimeChangedDialog();
    }

    _lastKnownTime = currentTime;
  }

  void _showTimeChangedDialog() {
    if (Get.isDialogOpen == true) return; // Prevent multiple dialogs

    // Show snackbar as primary notification
    Get.snackbar(
      'Time Changed',
      'Device time has been changed manually. This may affect countdown timers.',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 5),
      isDismissible: true,
      margin: const EdgeInsets.all(16),
    );

    // Also show dialog for more prominent notification
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!Get.isDialogOpen!) {
        Get.dialog(
          AlertDialog(
            title: const Text(
              'Time Changed',
              style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red),
            ),
            content: const Text(
              'Device time has been changed manually. This may affect countdown timers.',
              style: TextStyle(fontSize: 16),
            ),
          ),
          barrierDismissible: false,
        );

        // Automatically refresh session data and close dialog after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          if (Get.isDialogOpen!) {
            Get.back();
            // Refresh session data to recalculate timers
            try {
              final sessionController = Get.find<SessionController>();
              sessionController.loadSessions();
            } catch (e) {
              // SessionController might not be available
            }
          }
        });
      }
    });
  }

  void refreshData() {
    // This method will be called to refresh data on HomeScreen
    // For now, it will refresh user data which includes balance
    userController.refreshUserData();
    // You can add other refresh logic here if HomeScreen displays other dynamic data
  }

  // Test method to manually trigger time change detection
  void testTimeChangeDetection() {
    print('Manually triggering time change detection...');
    _showTimeChangedDialog();
  }
}
