import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zero_koin/controllers/user_controller.dart';
import 'package:zero_koin/controllers/session_controller.dart';

class HomeController extends GetxController {
  final UserController userController = Get.find<UserController>();

  // Time change detection variables
  Timer? _timeCheckTimer;
  DateTime? _lastKnownTime;
  static const Duration _timeCheckInterval = Duration(seconds: 5);
  static const Duration _timeChangeThreshold = Duration(seconds: 10);

  // Add any other observable variables or initial data loading specific to HomeScreen here
  // For now, we primarily rely on UserController's observables.

  @override
  void onInit() {
    super.onInit();
    // Initial data loading for HomeScreen, if any
    _initializeTimeChangeDetection();
  }

  @override
  void onClose() {
    _timeCheckTimer?.cancel();
    super.onClose();
  }

  void _initializeTimeChangeDetection() {
    _lastKnownTime = DateTime.now();
    _startTimeChangeDetection();
  }

  void _startTimeChangeDetection() {
    _timeCheckTimer = Timer.periodic(_timeCheckInterval, (timer) {
      _checkForTimeChange();
    });
  }

  void _checkForTimeChange() {
    if (_lastKnownTime == null) return;

    final currentTime = DateTime.now();
    final expectedTime = _lastKnownTime!.add(_timeCheckInterval);
    final timeDifference = currentTime.difference(expectedTime).abs();

    // If the time difference is greater than threshold, time was likely changed manually
    if (timeDifference > _timeChangeThreshold) {
      _showTimeChangedDialog();
    }

    _lastKnownTime = currentTime;
  }

  void _showTimeChangedDialog() {
    if (Get.isDialogOpen == true) return; // Prevent multiple dialogs

    Get.dialog(
      AlertDialog(
        title: const Text(
          'Time Changed',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red),
        ),
        content: const Text(
          'Device time has been changed manually. This may affect countdown timers.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              // Optionally refresh session data to recalculate timers
              try {
                final sessionController = Get.find<SessionController>();
                sessionController.loadSessions();
              } catch (e) {
                // SessionController might not be available
              }
            },
            child: const Text(
              'OK',
              style: TextStyle(
                color: Color(0xFF0682A2),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  void refreshData() {
    // This method will be called to refresh data on HomeScreen
    // For now, it will refresh user data which includes balance
    userController.refreshUserData();
    // You can add other refresh logic here if HomeScreen displays other dynamic data
  }
}
